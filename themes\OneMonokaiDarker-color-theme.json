{"name": "One Monokai", "type": "dark", "colors": {"activityBar.background": "#2F333D", "activityBar.foreground": "#D7DAE0", "activityBarBadge.background": "#528bff", "activityBarBadge.foreground": "#F8FAFD", "button.background": "#528bff", "debugToolBar.background": "#2F333D", "diffEditor.insertedTextBackground": "#00809B33", "dropdown.background": "#1a1d23", "dropdown.border": "#15171C", "editor.background": "#1e2227", "editor.findMatchBackground": "#42557B", "editor.findMatchHighlightBackground": "#31436500", "editor.lineHighlightBackground": "#2a2f38", "editor.selectionBackground": "#3a404d", "editorCursor.foreground": "#f8f8f0", "editorError.foreground": "#c24038", "editorGroup.emptyBackground": "#15171C", "editorGroup.border": "#15171C", "editorGroupHeader.tabsBackground": "#21252B", "editorIndentGuide.background1": "#353a42", "editorIndentGuide.activeBackground1": "#4a5162", "editorHoverWidget.background": "#21252B", "editorHoverWidget.border": "#15171C", "editorLineNumber.foreground": "#4a5162", "editorRuler.foreground": "#404040", "editorSuggestWidget.background": "#21252B", "editorSuggestWidget.border": "#15171C", "editorSuggestWidget.selectedBackground": "#252a33", "editorUnnecessaryCode.opacity": "#000000c0", "editorWhitespace.foreground": "#404248", "editorWidget.background": "#21252B", "input.background": "#1a1d23", "list.activeSelectionBackground": "#252a33", "list.activeSelectionForeground": "#d7dae0", "list.focusBackground": "#2a2f38", "list.highlightForeground": "#C5C5C5", "list.hoverBackground": "#232831", "list.inactiveSelectionBackground": "#252a33", "list.inactiveSelectionForeground": "#d7dae0", "notifications.background": "#21252B", "scrollbarSlider.activeBackground": "#747D9180", "scrollbarSlider.background": "#4E566680", "scrollbarSlider.hoverBackground": "#5A637580", "sideBar.background": "#21252B", "sideBarSectionHeader.background": "#1e2227", "statusBar.background": "#21252B", "statusBar.foreground": "#9da5b4", "statusBarItem.hoverBackground": "#252a33", "statusBar.noFolderBackground": "#21252B", "statusBar.debuggingBackground": "#21252B", "tab.activeBackground": "#2a2f38", "tab.border": "#15171C", "tab.inactiveBackground": "#21252B", "terminal.foreground": "#abb2bf", "terminal.ansiBlack": "#2d3139", "terminal.ansiBlue": "#528bff", "terminal.ansiGreen": "#98c379", "terminal.ansiYellow": "#e5c07b", "terminal.ansiCyan": "#56b6c2", "terminal.ansiMagenta": "#c678dd", "terminal.ansiRed": "#e06c75", "terminal.ansiWhite": "#d7dae0", "terminal.ansiBrightBlack": "#7f848e", "terminal.ansiBrightBlue": "#528bff", "terminal.ansiBrightGreen": "#98c379", "terminal.ansiBrightYellow": "#e5c07b", "terminal.ansiBrightCyan": "#56b6c2", "terminal.ansiBrightMagenta": "#7e0097", "terminal.ansiBrightRed": "#f44747", "terminal.ansiBrightWhite": "#d7dae0", "titleBar.activeBackground": "#1e2227", "titleBar.activeForeground": "#9da5b4", "titleBar.inactiveBackground": "#1e2227", "titleBar.inactiveForeground": "#6B717D"}, "tokenColors": [{"name": "Comment", "scope": ["comment", "string.comment"], "settings": {"foreground": "#676f7d"}}, {"name": "String", "scope": ["string", "string.template"], "settings": {"foreground": "#e5c07b"}}, {"name": "Number", "scope": "constant.numeric", "settings": {"foreground": "#c678dd"}}, {"name": "Embedded String Begin/End", "scope": ["string.embedded.begin", "string.embedded.end", "punctuation.definition.template-expression", "punctuation.section.embedded"], "settings": {"foreground": "#c678dd"}}, {"name": "Default Embedded Color", "scope": ["punctuation.section.embedded.begin.js", "punctuation.section.embedded.end.js", "punctuation.section.embedded.begin.erb", "punctuation.section.embedded.end.erb", "source.elixir.embedded", "punctuation.separator", "punctuation.accessor", "meta.brace"], "settings": {"foreground": "#abb2bf"}}, {"name": "Built-in constant", "scope": "constant.language", "settings": {"foreground": "#56b6c2"}}, {"name": "User-defined constant", "scope": ["constant.character", "constant.other"], "settings": {"foreground": "#56b6c2"}}, {"name": "Language Variable", "scope": "variable.language", "settings": {"foreground": "#e06c75"}}, {"name": "Keyword", "scope": ["keyword", "keyword.operator.logical", "keyword.operator.constructor"], "settings": {"foreground": "#e06c75"}}, {"name": "Keyword Operator", "scope": "keyword.operator", "settings": {"foreground": "#e06c75"}}, {"name": "Storage", "scope": "storage", "settings": {"foreground": "#e06c75"}}, {"name": "Types", "scope": "storage.type", "settings": {"foreground": "#56b6c2"}}, {"name": "Class", "scope": ["entity.name.class", "entity.name.module", "entity.name.type", "storage.identifier", "support.class"], "settings": {"foreground": "#61afef"}}, {"name": "Variable Object", "scope": ["variable.other.object", "variable.other.constant", "variable.other.global", "variable.other.readwrite.class", "variable.other.readwrite.instance", "variable.other.readwrite.batchfile", "variable.readwrite", "variable.readwrite.other.block"], "settings": {"foreground": "#61afef"}}, {"name": "Other variable", "scope": ["variable.other", "variable.other.property", "variable.other.block"], "settings": {"foreground": "#abb2bf"}}, {"name": "Inherited Class", "scope": "entity.other.inherited-class", "settings": {"foreground": "#98c379"}}, {"name": "Package Import", "scope": ["storage.modifier.import", "storage.modifier.package"], "settings": {"foreground": "#61afef"}}, {"name": "Function name", "scope": ["entity.name.function", "support.function"], "settings": {"foreground": "#98c379"}}, {"name": "Function argument", "scope": ["variable.parameter", "entity.name.variable.parameter", "parameter.variable"], "settings": {"fontStyle": "italic", "foreground": "#d19a66"}}, {"name": "Function call", "scope": "entity.name.function-call", "settings": {"foreground": "#abb2bf"}}, {"name": "Builtin Functions", "scope": ["function.support.builtin", "function.support.core"], "settings": {"foreground": "#98c379"}}, {"name": "Tag Name", "scope": ["entity.name.tag", "entity.name.tag.class.js"], "settings": {"foreground": "#e06c75"}}, {"name": "Tag Class and ID", "scope": ["entity.name.tag.class", "entity.name.tag.id"], "settings": {"foreground": "#56b6c2"}}, {"name": "Tag Attribute", "scope": "entity.other.attribute-name", "settings": {"foreground": "#98c379"}}, {"name": "Library constant", "scope": "support.constant", "settings": {"foreground": "#56b6c2"}}, {"name": "Library class/type", "scope": ["support.type", "support.variable"], "settings": {"foreground": "#56b6c2"}}, {"name": "Json Property", "scope": "support.dictionary.json", "settings": {"foreground": "#56b6c2"}}, {"name": "StyleSheet Property name", "scope": ["support.type.property-name.css", "support.type.property-name.scss", "support.type.property-name.less", "support.type.property-name.sass"], "settings": {"foreground": "#abb2bf"}}, {"name": "CSS: Pseudo Attribute Names", "scope": ["entity.other.attribute-name.pseudo-class.css", "entity.other.attribute-name.pseudo-class.scss", "entity.other.attribute-name.pseudo-class.less", "entity.other.attribute-name.pseudo-class.sass", "entity.other.attribute-name.pseudo-element.css", "entity.other.attribute-name.pseudo-element.scss", "entity.other.attribute-name.pseudo-element.less", "entity.other.attribute-name.pseudo-element.sass"], "settings": {"foreground": "#56b6c2"}}, {"name": "StyleSheet Property value", "scope": ["support.constant.css", "support.constant.scss", "support.constant.less", "support.constant.sass"], "settings": {"foreground": "#98c379"}}, {"name": "StyleSheet Variable", "scope": ["variable.css", "variable.scss", "variable.less", "variable.sass"], "settings": {"foreground": "#56b6c2"}}, {"name": "StyleSheet Variable String", "scope": ["variable.css.string", "variable.scss.string", "variable.less.string", "variable.sass.string"], "settings": {"foreground": "#e5c07b"}}, {"name": "StyleSheet Unit", "scope": ["unit.css", "unit.scss", "unit.less", "unit.sass"], "settings": {"foreground": "#c678dd"}}, {"name": "StyleSheet Function", "scope": ["function.css", "function.scss", "function.less", "function.sass"], "settings": {"foreground": "#56b6c2"}}, {"name": "Library variable", "scope": "support.other.variable", "settings": {"fontStyle": ""}}, {"name": "Invalid", "scope": "invalid", "settings": {"background": "#c678dd", "foreground": "#F8F8F0"}}, {"name": "Invalid deprecated", "scope": "invalid.deprecated", "settings": {"background": "#56b6c2", "foreground": "#F8F8F0"}}, {"name": "JSON String", "scope": "support.type.property-name.json", "settings": {"foreground": "#56b6c2"}}, {"name": "Link", "scope": "string.detected-link", "settings": {"foreground": "#61afef"}}, {"name": "diff.header", "scope": ["meta.diff", "meta.diff.header"], "settings": {"foreground": "#75715E"}}, {"name": "diff.deleted", "scope": "markup.deleted", "settings": {"foreground": "#e06c75"}}, {"name": "diff.inserted", "scope": "markup.inserted", "settings": {"foreground": "#98c379"}}, {"name": "diff.changed", "scope": "markup.changed", "settings": {"foreground": "#e5c07b"}}, {"scope": "constant.numeric.line-number.find-in-files - match", "settings": {"foreground": "#56b6c2A0"}}, {"scope": "entity.name.filename.find-in-files", "settings": {"foreground": "#e5c07b"}}, {"name": "Markup: Emphasis", "scope": "markup.italic, markup.italic.markdown", "settings": {"fontStyle": "italic"}}, {"name": "Markup: Punctuation", "scope": ["punctuation.definition.italic.markdown", "punctuation.definition.bold.markdown", "punctuation.definition.heading.markdown"], "settings": {"foreground": "#676f7d"}}, {"name": "Markup: <PERSON><PERSON><PERSON> Punctuation", "scope": "punctuation.definition.italic.markdown", "settings": {"fontStyle": "italic"}}, {"name": "Markdown: <PERSON>", "scope": "markup.underline.link.markdown", "settings": {"foreground": "#61afef"}}, {"name": "Markup: Bold", "scope": "markup.bold.markdown", "settings": {"fontStyle": "bold"}}, {"name": "Markup: Heading", "scope": "markup.heading.markdown", "settings": {"fontStyle": "bold", "foreground": "#e06c75"}}, {"name": "Markup: Quote", "scope": "markup.quote.markdown", "settings": {"foreground": "#98c379"}}, {"name": "Markup: <PERSON><PERSON><PERSON>", "scope": "meta.separator.markdown", "settings": {"foreground": "#c678dd", "fontStyle": "bold"}}, {"name": "Markup: <PERSON>", "scope": ["markup.inline.raw", "markup.raw.block.markdown"], "settings": {"foreground": "#56b6c2"}}, {"name": "Markup: List Punctuation", "scope": "punctuation.definition.list_item.markdown", "settings": {"fontStyle": "bold", "foreground": "#ffffff"}}]}